import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { fn } from "storybook/test";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./Tabs";

type TabsStoryProps = {
  defaultValue: string;
  orientation?: "horizontal" | "vertical";
  tab1Label: string;
  tab1Content: string;
  tab2Label: string;
  tab2Content: string;
  className?: string;
};

const meta: Meta<TabsStoryProps> = {
  title: "Components/Tabs",
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    defaultValue: { control: "text" },
    orientation: {
      control: "select",
      options: ["horizontal", "vertical"],
    },
    tab1Label: { control: "text" },
    tab1Content: { control: "text" },
    tab2Label: { control: "text" },
    tab2Content: { control: "text" },
    className: { control: "text" },
  },
  args: {
    onValueChange: fn(),
  },
  render: ({
    defaultValue,
    orientation,
    tab1Label,
    tab1Content,
    tab2Label,
    tab2Content,
    className,
  }: TabsStoryProps) => (
    <Tabs
      defaultValue={defaultValue}
      orientation={orientation}
      className={className}
      onValueChange={fn()}>
      <TabsList>
        <TabsTrigger value="tab1">{tab1Label}</TabsTrigger>
        <TabsTrigger value="tab2">{tab2Label}</TabsTrigger>
      </TabsList>
      <TabsContent value="tab1">{tab1Content}</TabsContent>
      <TabsContent value="tab2">{tab2Content}</TabsContent>
    </Tabs>
  ),
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultValue: "tab1",
    tab1Label: "Account",
    tab1Content: "Make changes to your account here.",
    tab2Label: "Password",
    tab2Content: "Change your password here.",
    className: "w-[400px]",
  },
};

export const Settings: Story = {
  args: {
    defaultValue: "tab1",
    tab1Label: "Profile",
    tab1Content: "Update your profile information and preferences here.",
    tab2Label: "Notifications",
    tab2Content: "Configure your notification settings and preferences.",
    className: "w-[500px]",
  },
};

export const Documentation: Story = {
  args: {
    defaultValue: "tab1",
    tab1Label: "Overview",
    tab1Content:
      "This is the overview tab content. Here you can see a summary of your data.",
    tab2Label: "API Reference",
    tab2Content: "View detailed API documentation and code examples here.",
    className: "w-[600px]",
  },
};
