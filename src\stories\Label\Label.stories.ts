import type { Meta, StoryObj } from "@storybook/nextjs";
import { Label } from "./Label";

const meta = {
  title: "Components/Label",
  component: Label,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["base", "error"],
    },
    htmlFor: { control: "text" },
    className: { control: "text" },
    children: { control: "text" },
  },
} satisfies Meta<typeof Label>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Base: Story = {
  args: {
    variant: "base",
    children: "Email address",
  },
};

export const Error: Story = {
  args: {
    variant: "error",
    children: "Invalid email address",
  },
};

export const Required: Story = {
  args: {
    variant: "base",
    children: "Password *",
  },
};

export const WithHtmlFor: Story = {
  render: (args) => (
    <div className="d-f fd-c g-2">
      <Label {...args} htmlFor="email-input">
        Email address
      </Label>
      <input 
        id="email-input" 
        type="email" 
        placeholder="Enter your email"
        className="p-2 border rounded"
      />
    </div>
  ),
  args: {
    variant: "base",
  },
};
