import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { Card, CardTitle, CardDescription } from "./Card";

type CardStoryProps = {
  variant: "base";
  title?: string;
  description?: string;
  className?: string;
};

const meta: Meta<CardStoryProps> = {
  title: "Components/Card",
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["base"],
    },
    title: { control: "text" },
    description: { control: "text" },
    className: { control: "text" },
  },
  render: ({ variant, title, description, className }: CardStoryProps) => (
    <Card variant={variant} className={className}>
      {title && <CardTitle>{title}</CardTitle>}
      {description && <CardDescription>{description}</CardDescription>}
    </Card>
  ),
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    variant: "base",
    title: "Card Title",
    description: "This is a simple card component. You can use cards to group related information and display content in a visually distinct container.",
  },
};

export const TitleOnly: Story = {
  args: {
    variant: "base",
    title: "Card with Title Only",
  },
};

export const DescriptionOnly: Story = {
  args: {
    variant: "base",
    description: "This card only has a description without a title.",
  },
};

export const ProductCard: Story = {
  args: {
    variant: "base",
    title: "Product Information",
    description: "Learn more about our latest product features and updates.",
  },
};
