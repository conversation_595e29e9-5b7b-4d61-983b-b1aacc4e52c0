import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import { fn } from "storybook/test";
import { Textarea } from "./Textarea";

const meta = {
  title: "Components/Textarea",
  component: Textarea,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["base", "error"],
    },
    placeholder: { control: "text" },
    disabled: { control: "boolean" },
    rows: { control: "number" },
    className: { control: "text" },
  },
  args: { 
    onChange: fn(),
    onFocus: fn(),
    onBlur: fn(),
  },
} satisfies Meta<typeof Textarea>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Base: Story = {
  args: {
    variant: "base",
    placeholder: "Type your message here...",
  },
};

export const Error: Story = {
  args: {
    variant: "error",
    placeholder: "This field is mandatory.",
  },
};

export const WithRows: Story = {
  args: {
    variant: "base",
    placeholder: "Type your message here...",
    rows: 6,
  },
};

export const Disabled: Story = {
  args: {
    variant: "base",
    placeholder: "Disabled textarea",
    disabled: true,
  },
};

export const WithValue: Story = {
  args: {
    variant: "base",
    defaultValue: "This is some pre-filled content in the textarea. You can edit this text.",
  },
};

export const LongPlaceholder: Story = {
  args: {
    variant: "base",
    placeholder: "Please provide a detailed description of your issue. Include any relevant information that might help us understand and resolve your problem more effectively.",
    rows: 4,
  },
};
