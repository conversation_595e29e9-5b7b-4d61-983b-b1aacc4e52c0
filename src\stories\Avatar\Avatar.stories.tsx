import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { Avatar, AvatarImage, AvatarFallback } from "./Avatar";

type AvatarStoryProps = {
  src?: string;
  fallbackText: string;
  alt?: string;
  className?: string;
};

const meta: Meta<AvatarStoryProps> = {
  title: "Components/Avatar",
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    src: { control: "text" },
    fallbackText: { control: "text" },
    alt: { control: "text" },
    className: { control: "text" },
  },
  render: ({ src, fallbackText, alt, className }: AvatarStoryProps) => (
    <Avatar className={className}>
      {src && <AvatarImage src={src} alt={alt} />}
      <AvatarFallback>{fallbackText}</AvatarFallback>
    </Avatar>
  ),
};

export default meta;
type Story = StoryObj<typeof meta>;

export const WithImage: Story = {
  args: {
    src: "https://avatars.githubusercontent.com/u/56491937?s=400&u=f4cccda3becdbd821128a86ff79fcc89dd407fcb&v=4",
    fallbackText: "RP",
    alt: "User avatar",
  },
};

export const WithFallback: Story = {
  args: {
    fallbackText: "RP",
  },
};

export const WithInitials: Story = {
  args: {
    fallbackText: "JD",
  },
};

export const WithBrokenImage: Story = {
  args: {
    src: "https://broken-image-url.com/image.jpg",
    fallbackText: "JD",
    alt: "Broken image",
  },
};
