{"name": "storybook", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "sb": "storybook dev -p 6006", "build-sb": "storybook build"}, "dependencies": {"@yummacss/ui": "file:~/desktop/yumma-lib/core/yumma-ui/yummacss-ui-0.1.0.tgz", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@storybook/nextjs": "9.1.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "eslint-plugin-storybook": "9.1.2", "storybook": "9.1.2", "typescript": "^5"}}