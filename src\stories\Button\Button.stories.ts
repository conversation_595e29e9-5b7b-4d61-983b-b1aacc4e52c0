import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import { fn } from "storybook/test";
import { Button } from "./Button";

const meta = {
  title: "Components/Button",
  component: Button,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["base", "destructive", "ghost", "link", "outline"],
    },
    className: { control: "text" },
    children: { control: "text" },
  },
  args: { onClick: fn() },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Base: Story = {
  args: {
    variant: "base",
    children: "Sign in",
  },
};

export const Destructive: Story = {
  args: {
    variant: "destructive",
    children: "Delete",
  },
};

export const Ghost: Story = {
  args: {
    variant: "ghost",
    children: "Watch",
  },
};

export const Link: Story = {
  args: {
    variant: "link",
    children: "Profile",
  },
};

export const Outline: Story = {
  args: {
    variant: "outline",
    children: "Buy",
  },
};
