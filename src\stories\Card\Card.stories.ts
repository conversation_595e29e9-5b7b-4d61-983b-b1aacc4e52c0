import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { Card, CardTitle, CardDescription } from "./Card";

const meta = {
  title: "Components/Card",
  component: Card,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["base"],
    },
    className: { control: "text" },
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    variant: "base",
    children: (
      <>
        <CardTitle>Card Title</CardTitle>
        <CardDescription>
          This is a simple card component. You can use cards to group related information and display content in a visually distinct container.
        </CardDescription>
      </>
    ),
  },
};

export const TitleOnly: Story = {
  args: {
    variant: "base",
    children: <CardTitle>Card with Title Only</CardTitle>,
  },
};

export const DescriptionOnly: Story = {
  args: {
    variant: "base",
    children: (
      <CardDescription>
        This card only has a description without a title.
      </CardDescription>
    ),
  },
};

export const CustomContent: Story = {
  args: {
    variant: "base",
    children: (
      <>
        <CardTitle>Product Information</CardTitle>
        <CardDescription>
          Learn more about our latest product features and updates.
        </CardDescription>
        <div className="mt-4">
          <p>Additional content can be added here.</p>
        </div>
      </>
    ),
  },
};
