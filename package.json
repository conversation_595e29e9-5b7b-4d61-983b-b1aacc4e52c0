{"name": "yumma-ui-storybook", "version": "0.1.0", "private": true, "scripts": {"format": "prettier --write .", "sb": "storybook dev -p 6006", "build-sb": "storybook build"}, "dependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.6.2", "@yummacss/ui": "file:~/desktop/yumma-lib/core/yumma-ui/yummacss-ui-0.1.0.tgz", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@storybook/nextjs": "9.1.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "eslint-plugin-storybook": "9.1.2", "storybook": "9.1.2", "typescript": "^5"}}