import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import { fn } from "storybook/test";
import { Input } from "./Input";

const meta = {
  title: "Components/Input",
  component: Input,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["base", "error"],
    },
    type: {
      control: "select",
      options: ["text", "email", "password", "number", "tel", "url"],
    },
    placeholder: { control: "text" },
    disabled: { control: "boolean" },
    className: { control: "text" },
  },
  args: { 
    onChange: fn(),
    onFocus: fn(),
    onBlur: fn(),
  },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Base: Story = {
  args: {
    variant: "base",
    placeholder: "<EMAIL>",
  },
};

export const Error: Story = {
  args: {
    variant: "error",
    placeholder: "Invalid email address",
  },
};

export const Email: Story = {
  args: {
    variant: "base",
    type: "email",
    placeholder: "Enter your email",
  },
};

export const Password: Story = {
  args: {
    variant: "base",
    type: "password",
    placeholder: "Enter your password",
  },
};

export const Number: Story = {
  args: {
    variant: "base",
    type: "number",
    placeholder: "Enter a number",
  },
};

export const Disabled: Story = {
  args: {
    variant: "base",
    placeholder: "Disabled input",
    disabled: true,
  },
};

export const WithValue: Story = {
  args: {
    variant: "base",
    defaultValue: "Pre-filled value",
  },
};
