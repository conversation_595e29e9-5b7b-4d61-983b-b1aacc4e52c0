import { create } from "storybook/theming";

export default create({
  base: "dark",
  brandTitle: "Yumma UI",
  brandImage: "/brand.png",
  brandUrl: "https://yummacss.com",
  brandTarget: "_self",

  // Scheme
  colorPrimary: "#413cb8",
  colorSecondary: "#bec6f2",

  // UI
  appBg: "#1e2039",
  appContentBg: "#21243f",
  appPreviewBg: "#151724",
  appBorderColor: "#2d3151",
  appBorderRadius: 4,

  // Text colors
  textColor: "#ffffff",
  textInverseColor: "#151724",

  // Toolbar default and active colors
  barTextColor: "#bec6f2",
  barSelectedColor: "#ffffffff",
  barHoverColor: "#ffffff",
  barBg: "#151724",

  // Form colors
  inputBg: "#1e2039",
  inputBorder: "#2d3151",
  inputTextColor: "#ffffff",
  inputBorderRadius: 2,
  buttonBg: "#21243f",
  booleanBg: "#151724",
  booleanSelectedBg: "#2a2d4c",
});
