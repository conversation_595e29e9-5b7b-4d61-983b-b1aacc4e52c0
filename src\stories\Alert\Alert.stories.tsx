import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, AlertTitle, AlertDescription } from "./Alert";

const meta = {
  title: "Components/Alert",
  component: Alert,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["base", "error", "info", "success", "warning"],
    },
    className: { control: "text" },
  },
} satisfies Meta<typeof Alert>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Base: Story = {
  args: {
    variant: "base",
    children: (
      <AlertContent>
        <AlertTitle>Base Alert</AlertTitle>
        <AlertDescription>
          This is a base alert. Use this for general information or notifications.
        </AlertDescription>
      </AlertContent>
    ),
  },
};

export const Error: Story = {
  args: {
    variant: "error",
    children: (
      <AlertContent>
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Something went wrong. Please try again or contact support if the problem persists.
        </AlertDescription>
      </AlertContent>
    ),
  },
};

export const Info: Story = {
  args: {
    variant: "info",
    children: (
      <AlertContent>
        <AlertTitle>Information</AlertTitle>
        <AlertDescription>
          This is an informational alert. Everything is working as expected.
        </AlertDescription>
      </AlertContent>
    ),
  },
};

export const Success: Story = {
  args: {
    variant: "success",
    children: (
      <AlertContent>
        <AlertTitle>Success</AlertTitle>
        <AlertDescription>
          Your operation was completed successfully!
        </AlertDescription>
      </AlertContent>
    ),
  },
};

export const Warning: Story = {
  args: {
    variant: "warning",
    children: (
      <AlertContent>
        <AlertTitle>Warning</AlertTitle>
        <AlertDescription>
          Please double-check your input. Some fields may be missing or incorrect.
        </AlertDescription>
      </AlertContent>
    ),
  },
};
