```tsx
<div className="mx-4 my-24">
    <h1 className="fs-3xl ta-l">@yummacss/ui</h1>
    <h2 className="fs-xxl my-4">Alert</h2>
    <div className="d-if cg-4">
    <div>
        <Alert variant="base">
        <AlertContent>
            <AlertTitle>Base Alert</AlertTitle>
            <AlertDescription>This is a base alert. Use this for general information or notifications.</AlertDescription>
        </AlertContent>
        </Alert>
    </div>

    <div>
        <Alert variant="error">
        <CircleX />
        <AlertContent>
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>Something went wrong. Please try again or contact support if the problem persists.</AlertDescription>
        </AlertContent>
        </Alert>
    </div>

    <div>
        <Alert variant="info">
        <Info />
        <AlertContent>
            <AlertTitle>Information</AlertTitle>
            <AlertDescription>This is an informational alert. Everything is working as expected.</AlertDescription>
        </AlertContent>
        </Alert>
    </div>

    <div>
        <Alert variant="success">
        <CircleCheck />
        <AlertContent>
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>Your operation was completed successfully!</AlertDescription>
        </AlertContent>
        </Alert>
    </div>

    <div>
        <Alert variant="warning">
        <OctagonAlert />
        <AlertContent>
            <AlertTitle>Warning</AlertTitle>
            <AlertDescription>Please double-check your input. Some fields may be missing or incorrect.</AlertDescription>
        </AlertContent>
        </Alert>
    </div>
    </div>

    <h2 className="fs-xxl my-4">Alert Dialog</h2>
    <div className="d-if cg-4">
    <AlertDialog>
        <AlertDialogTrigger asChild>
        <Button>Delete account</Button>
        </AlertDialogTrigger>
        <AlertDialogPortal>
        <AlertDialogOverlay />
        <AlertDialogContent size={"lg"}>
            <AlertDialogTitle>
            Are you absolutely sure?
            </AlertDialogTitle>
            <AlertDialogDescription >
            This action cannot be undone This will permanently delete your
            account and remove your data from our servers
            </AlertDialogDescription>
            <div className="d-f g-4 jc-fe">
            <AlertDialogCancel asChild>
                <Button variant={"ghost"}>Cancel</Button>
            </AlertDialogCancel>
            <AlertDialogAction asChild>
                <Button variant={"destructive"}>Yes, delete account</Button>
            </AlertDialogAction>
            </div>
        </AlertDialogContent>
        </AlertDialogPortal>
    </AlertDialog>

    </div>

    <h2 className="fs-xxl my-4">Avatar</h2>
    <div className="d-if cg-4">
    <div>
        <Avatar>
        <AvatarImage src="https://avatars.githubusercontent.com/u/********?s=400&u=f4cccda3becdbd821128a86ff79fcc89dd407fcb&v=4" />
        <AvatarFallback>RP</AvatarFallback>
        </Avatar>
    </div>

    <div>
        <Avatar>RP</Avatar>
    </div>
    </div>

    <h2 className="fs-xxl my-4">Badge</h2>
    <div className="d-if cg-4">
    <div>
        <Badge variant="base" size="xs">
        New
        </Badge>
    </div>

    <div>
        <Badge variant="destructive" size="xs">
        New
        </Badge>
    </div>

    <div>
        <Badge variant="outline" size="xs">
        New
        </Badge>
    </div>
    </div>

    <h2 className="fs-xxl my-4">Buttons</h2>
    <div className="d-if cg-4">
    <div>
        <Button className="h-8" variant="base">
        Sign in
        </Button>
    </div>
    <div>
        <Button className="h-8" variant="destructive">
        Delete
        </Button>
    </div>
    <div>
        <Button className="h-8" variant="ghost">
        Watch
        </Button>
    </div>
    <div>
        <Button className="h-8" variant="link">
        Profile
        </Button>
    </div>
    <div>
        <Button className="h-8" variant="outline">
        Buy
        </Button>
    </div>
    </div>

    <h2 className="fs-xxl my-4">Card</h2>
    <div className="">
    <Card variant="base">
        <CardTitle>Card Title</CardTitle>
        <CardDescription>
        This is a simple card component. You can use cards to group related information and display content in a visually distinct container.
        </CardDescription>
    </Card>
    </div>

    <h2 className="fs-xxl my-4">Checkbox</h2>
    <div className="d-f ai-c">
    <Checkbox defaultChecked id="accept" shape={"round"}>
        <CheckboxIndicator />
    </Checkbox>
    <label htmlFor="accept" className="fs-md tc-lead ml-2">
        Accept terms and conditions
    </label>
    </div>

    <h2 className="fs-xxl my-4">Dropdown Menu</h2>
    {/* TODO */}

    <h2 className="fs-xxl my-4">Input</h2>
    <div className="d-if cg-4">
    <div>
        <Input variant="base" placeholder="<EMAIL>" />
    </div>

    <div>
        <Input variant="error" placeholder="Invalid email address" />
    </div>
    </div>

    <h2 className="fs-xxl my-4">Label</h2>
    <div className="d-if cg-4">
    <div>
        <Label variant="base">Email address</Label>
    </div>

    <div>
        <Label variant="error">Invalid email address</Label>
    </div>
    </div>

    <h2 className="fs-xxl my-4">Textarea</h2>
    <div className="d-if cg-4">
    <div>
        <Textarea variant="base" placeholder="Type your message here..." />
    </div>

    <div>
        <Textarea variant="error" placeholder="This field is mandatory." />
    </div>
    </div>

    <h2 className="fs-xxl my-4">Tabs</h2>
    <div className="d-if cg-4">
    <Tabs defaultValue="account" className="w-[400px]">
        <TabsList>
        <TabsTrigger value="account">Account</TabsTrigger>
        <TabsTrigger value="password">Password</TabsTrigger>
        </TabsList>
        <TabsContent value="account">Make changes to your account here.</TabsContent>
        <TabsContent value="password">Change your password here.</TabsContent>
    </Tabs>
    </div>
</div>
    ```