import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import { Badge } from "./Badge";

const meta = {
  title: "Components/Badge",
  component: Badge,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["base", "destructive", "outline"],
    },
    size: {
      control: "select",
      options: ["xs", "sm", "md", "lg"],
    },
    className: { control: "text" },
    children: { control: "text" },
  },
} satisfies Meta<typeof Badge>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Base: Story = {
  args: {
    variant: "base",
    size: "xs",
    children: "New",
  },
};

export const Destructive: Story = {
  args: {
    variant: "destructive",
    size: "xs",
    children: "New",
  },
};

export const Outline: Story = {
  args: {
    variant: "outline",
    size: "xs",
    children: "New",
  },
};

export const Small: Story = {
  args: {
    variant: "base",
    size: "sm",
    children: "Small",
  },
};

export const Medium: Story = {
  args: {
    variant: "base",
    size: "md",
    children: "Medium",
  },
};

export const Large: Story = {
  args: {
    variant: "base",
    size: "lg",
    children: "Large",
  },
};
