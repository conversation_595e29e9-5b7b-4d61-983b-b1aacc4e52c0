import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import { Avatar, AvatarImage, AvatarFallback } from "./Avatar";

const meta = {
  title: "Components/Avatar",
  component: Avatar,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    className: { control: "text" },
  },
} satisfies Meta<typeof Avatar>;

export default meta;
type Story = StoryObj<typeof meta>;

export const WithImage: Story = {
  args: {
    children: (
      <>
        <AvatarImage src="https://avatars.githubusercontent.com/u/56491937?s=400&u=f4cccda3becdbd821128a86ff79fcc89dd407fcb&v=4" />
        <AvatarFallback>RP</AvatarFallback>
      </>
    ),
  },
};

export const WithFallback: Story = {
  args: {
    children: <AvatarFallback>RP</AvatarFallback>,
  },
};

export const WithText: Story = {
  args: {
    children: "RP",
  },
};

export const WithBrokenImage: Story = {
  args: {
    children: (
      <>
        <AvatarImage src="https://broken-image-url.com/image.jpg" />
        <AvatarFallback>JD</AvatarFallback>
      </>
    ),
  },
};
