import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";
import { But<PERSON> } from "../Button/Button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogOverlay,
  AlertDialogPortal,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./AlertDialog";

type AlertDialogStoryProps = {
  triggerText: string;
  triggerVariant: "base" | "destructive" | "ghost" | "link" | "outline";
  title: string;
  description: string;
  cancelText: string;
  actionText: string;
  actionVariant: "base" | "destructive" | "ghost" | "link" | "outline";
  size?: "sm" | "md" | "lg" | "xl";
  className?: string;
};

const meta: Meta<AlertDialogStoryProps> = {
  title: "Components/Alert Dialog",
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    triggerText: { control: "text" },
    triggerVariant: {
      control: "select",
      options: ["base", "destructive", "ghost", "link", "outline"],
    },
    title: { control: "text" },
    description: { control: "text" },
    cancelText: { control: "text" },
    actionText: { control: "text" },
    actionVariant: {
      control: "select",
      options: ["base", "destructive", "ghost", "link", "outline"],
    },
    size: {
      control: "select",
      options: ["sm", "md", "lg", "xl"],
    },
    className: { control: "text" },
  },
  render: ({
    triggerText,
    triggerVariant,
    title,
    description,
    cancelText,
    actionText,
    actionVariant,
    size,
    className,
  }: AlertDialogStoryProps) => (
    <AlertDialog className={className}>
      <AlertDialogTrigger asChild>
        <Button variant={triggerVariant}>{triggerText}</Button>
      </AlertDialogTrigger>
      <AlertDialogPortal>
        <AlertDialogOverlay />
        <AlertDialogContent size={size}>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
          <div className="d-f g-4 jc-fe">
            <AlertDialogCancel asChild>
              <Button variant="ghost">{cancelText}</Button>
            </AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button variant={actionVariant}>{actionText}</Button>
            </AlertDialogAction>
          </div>
        </AlertDialogContent>
      </AlertDialogPortal>
    </AlertDialog>
  ),
};

export default meta;
type Story = StoryObj<typeof meta>;

export const DeleteAccount: Story = {
  args: {
    triggerText: "Delete account",
    triggerVariant: "destructive",
    title: "Are you absolutely sure?",
    description:
      "This action cannot be undone. This will permanently delete your account and remove your data from our servers.",
    cancelText: "Cancel",
    actionText: "Yes, delete account",
    actionVariant: "destructive",
    size: "lg",
  },
};

export const SaveChanges: Story = {
  args: {
    triggerText: "Save changes",
    triggerVariant: "outline",
    title: "Save your changes?",
    description:
      "You have unsaved changes. Would you like to save them before leaving?",
    cancelText: "Don't save",
    actionText: "Save changes",
    actionVariant: "base",
    size: "lg",
  },
};

export const ConfirmAction: Story = {
  args: {
    triggerText: "Confirm",
    triggerVariant: "base",
    title: "Confirm your action",
    description: "Please confirm that you want to proceed with this action.",
    cancelText: "Cancel",
    actionText: "Confirm",
    actionVariant: "base",
    size: "md",
  },
};
