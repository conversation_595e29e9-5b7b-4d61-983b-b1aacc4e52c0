import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { <PERSON><PERSON>, <PERSON>ertContent, AlertDescription, AlertTitle } from "./Alert";

type AlertStoryProps = {
  variant: "base" | "error" | "info" | "success" | "warning";
  title: string;
  description: string;
  className?: string;
};

const meta: Meta<AlertStoryProps> = {
  title: "Components/Alert",
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: "select",
      options: ["base", "error", "info", "success", "warning"],
    },
    title: { control: "text" },
    description: { control: "text" },
    className: { control: "text" },
  },
  render: ({ variant, title, description, className }: AlertStoryProps) => (
    <Alert variant={variant} className={className}>
      <AlertContent>
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription>{description}</AlertDescription>
      </AlertContent>
    </Alert>
  ),
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Base: Story = {
  args: {
    variant: "base",
    title: "Base Alerta",
    description:
      "This is a base alert. Use this for general information or notifications.",
  },
};

export const Error: Story = {
  args: {
    variant: "error",
    title: "Error",
    description:
      "Something went wrong. Please try again or contact support if the problem persists.",
  },
};

export const Info: Story = {
  args: {
    variant: "info",
    title: "Information",
    description:
      "This is an informational alert. Everything is working as expected.",
  },
};

export const Success: Story = {
  args: {
    variant: "success",
    title: "Success",
    description: "Your operation was completed successfully!",
  },
};

export const Warning: Story = {
  args: {
    variant: "warning",
    title: "Warning",
    description:
      "Please double-check your input. Some fields may be missing or incorrect.",
  },
};
