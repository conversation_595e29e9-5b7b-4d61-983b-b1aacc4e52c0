import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import { fn } from "storybook/test";
import { Checkbox, CheckboxIndicator } from "./Checkbox";

const meta = {
  title: "Components/Checkbox",
  component: Checkbox,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    shape: {
      control: "select",
      options: ["square", "round"],
    },
    defaultChecked: { control: "boolean" },
    disabled: { control: "boolean" },
    className: { control: "text" },
  },
  args: { 
    onCheckedChange: fn(),
  },
} satisfies Meta<typeof Checkbox>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    shape: "square",
    children: <CheckboxIndicator />,
  },
};

export const Checked: Story = {
  args: {
    shape: "square",
    defaultChecked: true,
    children: <CheckboxIndicator />,
  },
};

export const Round: Story = {
  args: {
    shape: "round",
    children: <CheckboxIndicator />,
  },
};

export const RoundChecked: Story = {
  args: {
    shape: "round",
    defaultChecked: true,
    children: <CheckboxIndicator />,
  },
};

export const Disabled: Story = {
  args: {
    shape: "square",
    disabled: true,
    children: <CheckboxIndicator />,
  },
};

export const DisabledChecked: Story = {
  args: {
    shape: "square",
    disabled: true,
    defaultChecked: true,
    children: <CheckboxIndicator />,
  },
};

export const WithLabel: Story = {
  render: (args) => (
    <div className="d-f ai-c">
      <Checkbox {...args} id="accept">
        <CheckboxIndicator />
      </Checkbox>
      <label htmlFor="accept" className="fs-md tc-lead ml-2">
        Accept terms and conditions
      </label>
    </div>
  ),
  args: {
    shape: "round",
    defaultChecked: true,
  },
};
