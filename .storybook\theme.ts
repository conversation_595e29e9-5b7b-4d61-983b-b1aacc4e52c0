import { create } from "storybook/theming";

export default create({
    base: "dark",
    brandTitle: "Yumma UI",
    brandImage: "/brand.png",
    brandUrl: "https://yummacss.com",
    brandTarget: "_self",

    // Scheme
    colorPrimary: '#413cb8',
    colorSecondary: '#2d3151',

    // UI
    appBg: '#21243f',
    appContentBg: '#21243f',
    appPreviewBg: '#151724',
    appBorderColor: '#2d3151',
    appBorderRadius: 4,
    
    // Text colors
    textColor: '#ffffff',
    textInverseColor: '#ffffff',
    
    // Toolbar default and active colors
    barTextColor: '#bec6f2',
    barSelectedColor: '#ffffffff',
    barHoverColor: '#ffffff',
    barBg: '#151724',
    
    // Form colors
    inputBg: '#1e2039',
    inputBorder: '#2d3151',
    inputTextColor: '#ffffff',
    inputBorderRadius: 2,
});