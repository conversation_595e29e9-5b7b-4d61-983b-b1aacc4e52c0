import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import { fn } from "storybook/test";
import { Ta<PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger, TabsContent } from "./Tabs";

const meta = {
  title: "Components/Tabs",
  component: Tabs,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    defaultValue: { control: "text" },
    orientation: {
      control: "select",
      options: ["horizontal", "vertical"],
    },
    className: { control: "text" },
  },
  args: { 
    onValueChange: fn(),
  },
} satisfies Meta<typeof Tabs>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    defaultValue: "account",
    className: "w-[400px]",
    children: (
      <>
        <TabsList>
          <TabsTrigger value="account">Account</TabsTrigger>
          <TabsTrigger value="password">Password</TabsTrigger>
        </TabsList>
        <TabsContent value="account">
          Make changes to your account here.
        </TabsContent>
        <TabsContent value="password">
          Change your password here.
        </TabsContent>
      </>
    ),
  },
};

export const ThreeTabs: Story = {
  args: {
    defaultValue: "overview",
    className: "w-[500px]",
    children: (
      <>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-2">Overview</h3>
            <p>This is the overview tab content. Here you can see a summary of your data.</p>
          </div>
        </TabsContent>
        <TabsContent value="analytics">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-2">Analytics</h3>
            <p>View detailed analytics and metrics here.</p>
          </div>
        </TabsContent>
        <TabsContent value="settings">
          <div className="p-4">
            <h3 className="text-lg font-semibold mb-2">Settings</h3>
            <p>Configure your preferences and settings.</p>
          </div>
        </TabsContent>
      </>
    ),
  },
};

export const WithRichContent: Story = {
  args: {
    defaultValue: "profile",
    className: "w-[600px]",
    children: (
      <>
        <TabsList>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>
        <TabsContent value="profile">
          <div className="p-4 space-y-4">
            <h3 className="text-lg font-semibold">Profile Settings</h3>
            <div className="space-y-2">
              <label className="block text-sm font-medium">Name</label>
              <input type="text" className="w-full p-2 border rounded" placeholder="Your name" />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium">Email</label>
              <input type="email" className="w-full p-2 border rounded" placeholder="<EMAIL>" />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="notifications">
          <div className="p-4 space-y-4">
            <h3 className="text-lg font-semibold">Notification Preferences</h3>
            <div className="space-y-3">
              <label className="flex items-center space-x-2">
                <input type="checkbox" defaultChecked />
                <span>Email notifications</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="checkbox" />
                <span>Push notifications</span>
              </label>
              <label className="flex items-center space-x-2">
                <input type="checkbox" defaultChecked />
                <span>SMS notifications</span>
              </label>
            </div>
          </div>
        </TabsContent>
      </>
    ),
  },
};
